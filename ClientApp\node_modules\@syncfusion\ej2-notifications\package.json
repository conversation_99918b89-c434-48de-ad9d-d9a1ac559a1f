{"_from": "@syncfusion/ej2-notifications@*", "_id": "@syncfusion/ej2-notifications@27.1.48", "_inBundle": false, "_integrity": "sha512-FyFlIQcDkXmjELPExuw5ieevr8sMceev25FRPJMpVGjdWAmX4emChWAtMU5v9CrNc/qrvLAfuEaD8h41ZerspQ==", "_location": "/@syncfusion/ej2-notifications", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@syncfusion/ej2-notifications@*", "name": "@syncfusion/ej2-notifications", "escapedName": "@syncfusion%2fej2-notifications", "scope": "@syncfusion", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/", "/@syncfusion/ej2", "/@syncfusion/ej2-angular-notifications", "/@syncfusion/ej2-dropdowns", "/@syncfusion/ej2-gantt", "/@syncfusion/ej2-grids", "/@syncfusion/ej2-inplace-editor", "/@syncfusion/ej2-interactive-chat", "/@syncfusion/ej2-kanban", "/@syncfusion/ej2-pdfviewer", "/@syncfusion/ej2-react-notifications", "/@syncfusion/ej2-vue-notifications"], "_resolved": "https://nexus.syncfusioninternal.com/repository/ej2-hotfix-new/@syncfusion/ej2-notifications/-/ej2-notifications-27.1.48.tgz", "_shasum": "f746613404eec44c0b216e8b6ecb7a8a415ad134", "_spec": "@syncfusion/ej2-notifications@*", "_where": "/jenkins/workspace/elease-automation_release_27.1.1/packages/included", "author": {"name": "Syncfusion Inc."}, "bundleDependencies": false, "dependencies": {"@syncfusion/ej2-base": "~27.1.50", "@syncfusion/ej2-buttons": "~27.1.50", "@syncfusion/ej2-popups": "~27.1.50"}, "deprecated": false, "description": "A package of Essential JS 2 notification components such as Toast and Badge which used to notify important information to end-users.", "devDependencies": {}, "es2015": "./dist/es6/ej2-notifications.es5.js", "keywords": ["ej2", "syncfusion", "web-components", "javascript", "typescript", "ej2-notification-components", "ej2-badge", "badges", "ej2-toast", "toast", "toaster", "notification", "notification badge", "circle badge", "badge css", "toast css", "alart"], "license": "SEE LICENSE IN license", "main": "./dist/ej2-notifications.umd.min.js", "module": "./index.js", "name": "@syncfusion/ej2-notifications", "repository": {"type": "git", "url": "https://github.com/syncfusion/ej2-javascript-ui-controls/tree/master/controls/notifications"}, "typings": "index.d.ts", "version": "27.1.50", "sideEffects": false, "homepage": "https://www.syncfusion.com/javascript-ui-controls"}