{"_from": "@syncfusion/ej2-buttons@*", "_id": "@syncfusion/ej2-buttons@27.1.51", "_inBundle": false, "_integrity": "sha512-opk77V+a3Fye0gcpmw/59MlVS+qRZe3+cjU0gJIHRtKd2cbTMgx+XSq6N1qUYWt0GPEEMna9l5NU6qKhI/F50A==", "_location": "/@syncfusion/ej2-buttons", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@syncfusion/ej2-buttons@*", "name": "@syncfusion/ej2-buttons", "escapedName": "@syncfusion%2fej2-buttons", "scope": "@syncfusion", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/", "/@syncfusion/ej2", "/@syncfusion/ej2-angular-buttons", "/@syncfusion/ej2-calendars", "/@syncfusion/ej2-diagrams", "/@syncfusion/ej2-documenteditor", "/@syncfusion/ej2-filemanager", "/@syncfusion/ej2-gantt", "/@syncfusion/ej2-grids", "/@syncfusion/ej2-image-editor", "/@syncfusion/ej2-inplace-editor", "/@syncfusion/ej2-inputs", "/@syncfusion/ej2-kanban", "/@syncfusion/ej2-lists", "/@syncfusion/ej2-maps", "/@syncfusion/ej2-navigations", "/@syncfusion/ej2-notifications", "/@syncfusion/ej2-pdfviewer", "/@syncfusion/ej2-pivotview", "/@syncfusion/ej2-popups", "/@syncfusion/ej2-querybuilder", "/@syncfusion/ej2-react-buttons", "/@syncfusion/ej2-ribbon", "/@syncfusion/ej2-richtexteditor", "/@syncfusion/ej2-schedule", "/@syncfusion/ej2-vue-buttons"], "_resolved": "https://nexus.syncfusioninternal.com/repository/ej2-hotfix-new/@syncfusion/ej2-buttons/-/ej2-buttons-27.1.51.tgz", "_shasum": "fd999306fc85fae9398290fb4d736b8317cdd2d4", "_spec": "@syncfusion/ej2-buttons@*", "_where": "/jenkins/workspace/elease-automation_release_27.1.1/packages/included", "author": {"name": "Syncfusion Inc."}, "bugs": {"url": "https://github.com/syncfusion/ej2-javascript-ui-controls/issues"}, "bundleDependencies": false, "dependencies": {"@syncfusion/ej2-base": "~27.1.53"}, "deprecated": false, "description": "A package of feature-rich Essential JS 2 components such as Button, CheckBox, RadioButton and Switch.", "devDependencies": {}, "es2015": "./dist/es6/ej2-buttons.es5.js", "homepage": "https://www.syncfusion.com/javascript-ui-controls", "keywords": ["ej2", "syncfusion", "JavaScript", "TypeScript", "ej2-buttons", "button", "ej2 button", "checkbox", "ej2 checkbox", "checkboxes", "radio button", "radiobutton", "radiobuttons", "ej2 radiobutton", "switch", "ej2 switch", "primary button", "flat button", "round button", "icon button", "to<PERSON><PERSON><PERSON>", "toggle button", "form control", "form controls", "input", "floating action button", "floating button", "fab"], "license": "SEE LICENSE IN license", "main": "./dist/ej2-buttons.umd.min.js", "module": "./index.js", "name": "@syncfusion/ej2-buttons", "repository": {"type": "git", "url": "git+https://github.com/syncfusion/ej2-javascript-ui-controls.git"}, "typings": "index.d.ts", "version": "27.1.53", "sideEffects": false}