{"_from": "@syncfusion/ej2-angular-splitbuttons@*", "_id": "@syncfusion/ej2-angular-splitbuttons@27.1.50", "_inBundle": false, "_integrity": "sha512-9quntc0PEQnYe2iCVc3fC7Ir69DZXmXQsjssXe2325QR/VMmO2gQ3+99mcl5vXadloic0AP1EQjBB+mc2MwUaQ==", "_location": "/@syncfusion/ej2-angular-splitbuttons", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@syncfusion/ej2-angular-splitbuttons@*", "name": "@syncfusion/ej2-angular-splitbuttons", "escapedName": "@syncfusion%2fej2-angular-splitbuttons", "scope": "@syncfusion", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/"], "_resolved": "https://nexus.syncfusioninternal.com/repository/ej2-angular-hotfix/@syncfusion/ej2-angular-splitbuttons/-/ej2-angular-splitbuttons-27.1.50.tgz", "_shasum": "1eb48a50341f2a6dde14318637945a2dfbf5ef87", "_spec": "@syncfusion/ej2-angular-splitbuttons@*", "_where": "/jenkins/workspace/elease-automation_release_27.1.1/ivypackages/included", "author": {"name": "Syncfusion Inc."}, "bugs": {"url": "https://github.com/syncfusion/ej2-angular-ui-components/issues"}, "bundleDependencies": false, "dependencies": {"@syncfusion/ej2-angular-base": "~27.1.50", "@syncfusion/ej2-base": "~27.1.55", "@syncfusion/ej2-splitbuttons": "27.1.56"}, "deprecated": false, "description": "A package of feature-rich Essential JS 2 components such as DropDownButton, SplitButton, ProgressButton and ButtonGroup. for Angular", "es2020": "fesm2020/syncfusion-ej2-angular-splitbuttons.mjs", "esm2020": "esm2020/syncfusion-ej2-angular-splitbuttons.mjs", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./syncfusion-ej2-angular-splitbuttons.d.ts", "esm2020": "./esm2020/syncfusion-ej2-angular-splitbuttons.mjs", "es2020": "./fesm2020/syncfusion-ej2-angular-splitbuttons.mjs", "es2015": "./fesm2015/syncfusion-ej2-angular-splitbuttons.mjs", "node": "./fesm2015/syncfusion-ej2-angular-splitbuttons.mjs", "default": "./fesm2020/syncfusion-ej2-angular-splitbuttons.mjs"}}, "fesm2015": "fesm2015/syncfusion-ej2-angular-splitbuttons.mjs", "fesm2020": "fesm2020/syncfusion-ej2-angular-splitbuttons.mjs", "homepage": "https://www.syncfusion.com/angular-components", "keywords": ["angular", "ng", "ej2-ng-splitbuttons", "ng-splitbutton", "ng-dropdownbutton", "ng progress button", "angular progress button", "ng spin button", "angular spin button", "ng progress", "angular progress", "ng progress indicator", "angular progress indicator", "ng spinner", "angular spinner"], "license": "SEE LICENSE IN license", "module": "fesm2015/syncfusion-ej2-angular-splitbuttons.mjs", "name": "@syncfusion/ej2-angular-splitbuttons", "repository": {"type": "git", "url": "git+https://github.com/syncfusion/ej2-angular-ui-components.git"}, "schematics": "./schematics/collection.json", "sideEffects": false, "typings": "syncfusion-ej2-angular-splitbuttons.d.ts", "version": "27.1.56"}