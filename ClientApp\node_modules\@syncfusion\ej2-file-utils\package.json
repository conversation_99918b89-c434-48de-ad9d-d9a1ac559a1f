{"_from": "@syncfusion/ej2-file-utils@*", "_id": "@syncfusion/ej2-file-utils@27.1.48", "_inBundle": false, "_integrity": "sha512-5VXZUlCl+20wqndbVqEzoi7O1OzbMS3jg/EpjJeVdfk+XppmRNn0fMsg0IwHCJ4UOeUCENlG7aAHix+mikh6QQ==", "_location": "/@syncfusion/ej2-file-utils", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@syncfusion/ej2-file-utils@*", "name": "@syncfusion/ej2-file-utils", "escapedName": "@syncfusion%2fej2-file-utils", "scope": "@syncfusion", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/", "/@syncfusion/ej2", "/@syncfusion/ej2-compression", "/@syncfusion/ej2-documenteditor", "/@syncfusion/ej2-grids", "/@syncfusion/ej2-heatmap", "/@syncfusion/ej2-maps", "/@syncfusion/ej2-pivotview", "/@syncfusion/ej2-treemap"], "_resolved": "https://nexus.syncfusioninternal.com/repository/ej2-hotfix-new/@syncfusion/ej2-file-utils/-/ej2-file-utils-27.1.48.tgz", "_shasum": "4b5b21c8b6e91dc8eb9f521e533011ac35e4b374", "_spec": "@syncfusion/ej2-file-utils@*", "_where": "/jenkins/workspace/elease-automation_release_27.1.1/packages/included", "author": {"name": "Syncfusion Inc."}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Essential JS 2 File Library", "devDependencies": {}, "es2015": "./dist/es6/ej2-file-utils.es5.js", "license": "SEE LICENSE IN license", "main": "./dist/ej2-file-utils.umd.min.js", "module": "./index.js", "name": "@syncfusion/ej2-file-utils", "typings": "index.d.ts", "version": "27.1.50", "sideEffects": false, "homepage": "https://www.syncfusion.com/javascript-ui-controls"}