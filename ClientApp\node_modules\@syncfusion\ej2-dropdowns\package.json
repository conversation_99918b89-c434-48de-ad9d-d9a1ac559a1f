{"_from": "@syncfusion/ej2-dropdowns@*", "_id": "@syncfusion/ej2-dropdowns@27.1.52", "_inBundle": false, "_integrity": "sha512-lgrKKbthjGtnEjuwnAZd+5EzMt2JLyNRVUP0+SPCG7vJWyXFle2NiJX0FKIogrUk2Idaif+qtcs9CXIBh8NOBw==", "_location": "/@syncfusion/ej2-dropdowns", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@syncfusion/ej2-dropdowns@*", "name": "@syncfusion/ej2-dropdowns", "escapedName": "@syncfusion%2fej2-dropdowns", "scope": "@syncfusion", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/", "/@syncfusion/ej2", "/@syncfusion/ej2-angular-dropdowns", "/@syncfusion/ej2-documenteditor", "/@syncfusion/ej2-gantt", "/@syncfusion/ej2-grids", "/@syncfusion/ej2-image-editor", "/@syncfusion/ej2-inplace-editor", "/@syncfusion/ej2-kanban", "/@syncfusion/ej2-pdfviewer", "/@syncfusion/ej2-pivotview", "/@syncfusion/ej2-querybuilder", "/@syncfusion/ej2-react-dropdowns", "/@syncfusion/ej2-ribbon", "/@syncfusion/ej2-richtexteditor", "/@syncfusion/ej2-schedule", "/@syncfusion/ej2-spreadsheet", "/@syncfusion/ej2-vue-dropdowns"], "_resolved": "https://nexus.syncfusioninternal.com/repository/ej2-hotfix-new/@syncfusion/ej2-dropdowns/-/ej2-dropdowns-27.1.52.tgz", "_shasum": "f5fe34cbcdf22285b14cce67aba1da75d36b301d", "_spec": "@syncfusion/ej2-dropdowns@*", "_where": "/jenkins/workspace/elease-automation_release_27.1.1/packages/included", "author": {"name": "Syncfusion Inc."}, "bundleDependencies": false, "dependencies": {"@syncfusion/ej2-base": "~27.1.53", "@syncfusion/ej2-data": "~27.1.52", "@syncfusion/ej2-inputs": "~27.1.50", "@syncfusion/ej2-lists": "~27.1.50", "@syncfusion/ej2-navigations": "~27.1.53", "@syncfusion/ej2-notifications": "~27.1.50", "@syncfusion/ej2-popups": "~27.1.53"}, "deprecated": false, "description": "Essential JS 2 DropDown Components", "devDependencies": {}, "es2015": "./dist/es6/ej2-dropdowns.es5.js", "keywords": ["ej2", "syncfusion", "ej2-dropdown", "dropdownlist", "autocomplete", "multiselect", "combobox", "select", "ej2-dropdownlist", "ej2-autocomplete", "ej2-multiselect", "ej2-combobox"], "license": "SEE LICENSE IN license", "main": "./dist/ej2-dropdowns.umd.min.js", "module": "./index.js", "name": "@syncfusion/ej2-dropdowns", "typings": "index.d.ts", "version": "27.1.53", "sideEffects": false, "homepage": "https://www.syncfusion.com/javascript-ui-controls"}