import { InnobookModalWrapperComponent } from 'app/core/innobook-modal-wrapper/innobook-modal-wrapper.component';
import { ToastService } from 'app/service/toast.service';
import { CodeInputComponent, CodeInputModule } from 'angular-code-input';
import { Component, DestroyRef, inject, Inject, OnInit, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { AuthenticationService } from '../service/authentication.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SpinnerService } from 'app/service/spinner.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
const inputs = document.querySelectorAll(".otp-field > input");
@Component({
  selector: 'app-factor-code',
  standalone: true,
  imports: [CodeInputModule, TranslateModule, InnobookModalWrapperComponent],
  templateUrl: './factor-code.component.html',
  styleUrl: './factor-code.component.scss'
})
export class FactorCodeComponent implements OnInit {
  _code: string = ''
  private spinnerService = inject(SpinnerService)
  private _toastService = inject(ToastService)
  private auth_services = inject(AuthenticationService)
  private translate = inject(TranslateService)
  static getComponent(): typeof FactorCodeComponent {
    return FactorCodeComponent;
  }
  @ViewChild('codeInput') codeInput !: CodeInputComponent;
  constructor(@Inject(MAT_DIALOG_DATA) public data: any,
    private dialogRef: MatDialogRef<FactorCodeComponent>

  ) { }
  destroyRef = inject(DestroyRef);
  ngOnInit() {

  }
  CloseDia(data: any) {
    this.dialogRef?.close(data);
  }
  goBack() {

    this.dialogRef?.close();

  }
  ResendCode() {
    this.auth_services.ResendCode(this.data.email).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.clearCodeInput();
        this._toastService.showSuccess(this.translate.instant("TOAST.Sent"), this.translate.instant("TOAST.SentCode"))
      }
    }
    )
  }

  clearCodeInput() {
    this._code = null;
    this.codeInput.reset();
  }
  Login() {
    this.spinnerService.show()
    this.auth_services.SignIn(this.data.email, this._code).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.CloseDia(res)
      }
      else {
        this._toastService.showError(this.translate.instant("TOAST.Fail"), this.translate.instant("TOAST.Fail"))
      }
    }
    )


  }

  onCodeChanged(value: any) {
    this._code = value;
    if (this._code.length == 6) {
      this.Login();
    }

  }
}
