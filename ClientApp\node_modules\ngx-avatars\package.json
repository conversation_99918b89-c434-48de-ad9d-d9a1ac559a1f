{"name": "ngx-avatars", "description": "A universal avatar component for Angular applications that fetches / generates avatar based on the information you have about the user.", "version": "1.8.0", "keywords": ["angular", "avatar", "avatars", "badge", "gravatar", "component", "initials"], "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/Heatmanofurioso", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/Heatmanofurioso/ngx-avatars"}, "sideEffects": false, "dependencies": {"ts-md5": "^1.3.1", "tslib": "^2.6.3"}, "peerDependencies": {"@angular/common": "^18.0.1", "@angular/core": "^18.0.1"}, "module": "fesm2022/ngx-avatars.mjs", "typings": "index.d.ts", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "esm2022": "./esm2022/ngx-avatars.mjs", "esm": "./esm2022/ngx-avatars.mjs", "default": "./fesm2022/ngx-avatars.mjs"}}}