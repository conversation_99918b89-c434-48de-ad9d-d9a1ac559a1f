{"name": "@ngx-translate/core", "version": "15.0.0", "main": "public-api.ts", "componentId": {"scope": "ngx-translate.packages", "name": "core", "version": "15.0.0"}, "dependencies": {}, "devDependencies": {"@angular/router": ">=16.0.0", "@types/jest": "^29.5.0", "@types/node": "^16.11.7", "jest": "^29.5.0", "jest-preset-angular": "~13.1.0", "@teambit/angular-v16": "1.0.2"}, "peerDependencies": {"rxjs": "^6.5.5 || ^7.4.0", "@angular/common": ">=16.0.0", "@angular/core": ">=16.0.0"}, "license": "SEE LICENSE IN LICENSE", "private": false, "engines": {"node": "^16.13.0 || >=18.10.0"}, "sideEffects": false, "module": "dist/fesm2022/ngx-translate-core.mjs", "typings": "dist/index.d.ts", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./dist/index.d.ts", "esm2022": "./dist/esm2022/ngx-translate-core.mjs", "esm": "./esm2022/ngx-translate-core.mjs", "default": "./dist/fesm2022/ngx-translate-core.mjs"}}}