{"_from": "@syncfusion/ej2-angular-grids@*", "_id": "@syncfusion/ej2-angular-grids@27.1.51", "_inBundle": false, "_integrity": "sha512-XZowiI65J2kE3Ms3CeYG/2g7C//DjsVB4QPM7DkuGQDVBDkpAuJB4F/jh7K95ZAfFxG0PxqggGzH0IybdJGcvA==", "_location": "/@syncfusion/ej2-angular-grids", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@syncfusion/ej2-angular-grids@*", "name": "@syncfusion/ej2-angular-grids", "escapedName": "@syncfusion%2fej2-angular-grids", "scope": "@syncfusion", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/"], "_resolved": "https://nexus.syncfusioninternal.com/repository/ej2-angular-hotfix/@syncfusion/ej2-angular-grids/-/ej2-angular-grids-27.1.51.tgz", "_shasum": "d6d396ad6e09f320f44132a1dd56c89b3f550923", "_spec": "@syncfusion/ej2-angular-grids@*", "_where": "/jenkins/workspace/elease-automation_release_27.1.1/ivypackages/included", "author": {"name": "Syncfusion Inc."}, "bugs": {"url": "https://github.com/syncfusion/ej2-angular-ui-components/issues"}, "bundleDependencies": false, "dependencies": {"@syncfusion/ej2-angular-base": "~27.1.50", "@syncfusion/ej2-base": "~27.1.52", "@syncfusion/ej2-grids": "27.1.52"}, "deprecated": false, "description": "Feature-rich JavaScript datagrid (datatable) control with built-in support for editing, filtering, grouping, paging, sorting, and exporting to Excel. for Angular", "es2020": "fesm2020/syncfusion-ej2-angular-grids.mjs", "esm2020": "esm2020/syncfusion-ej2-angular-grids.mjs", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./syncfusion-ej2-angular-grids.d.ts", "esm2020": "./esm2020/syncfusion-ej2-angular-grids.mjs", "es2020": "./fesm2020/syncfusion-ej2-angular-grids.mjs", "es2015": "./fesm2015/syncfusion-ej2-angular-grids.mjs", "node": "./fesm2015/syncfusion-ej2-angular-grids.mjs", "default": "./fesm2020/syncfusion-ej2-angular-grids.mjs"}}, "fesm2015": "fesm2015/syncfusion-ej2-angular-grids.mjs", "fesm2020": "fesm2020/syncfusion-ej2-angular-grids.mjs", "homepage": "https://www.syncfusion.com/angular-components", "keywords": ["angular", "ng-grids", "ej2-angular-grids", "ej2-ng-grids"], "license": "SEE LICENSE IN license", "module": "fesm2015/syncfusion-ej2-angular-grids.mjs", "name": "@syncfusion/ej2-angular-grids", "repository": {"type": "git", "url": "git+https://github.com/syncfusion/ej2-angular-ui-components.git"}, "schematics": "./schematics/collection.json", "sideEffects": false, "typings": "syncfusion-ej2-angular-grids.d.ts", "version": "27.1.52"}