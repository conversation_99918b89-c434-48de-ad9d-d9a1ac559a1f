{"_from": "@syncfusion/ej2-angular-notifications@*", "_id": "@syncfusion/ej2-angular-notifications@27.1.48", "_inBundle": false, "_integrity": "sha512-gOGXxAbWCimNXSUdUrskrznCeAxHDKqNYcwaukN13WdL+JTx/5lHMVv0f+z/FvJMDe+iAemyU7rcOnCbzB9WCg==", "_location": "/@syncfusion/ej2-angular-notifications", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@syncfusion/ej2-angular-notifications@*", "name": "@syncfusion/ej2-angular-notifications", "escapedName": "@syncfusion%2fej2-angular-notifications", "scope": "@syncfusion", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/"], "_resolved": "https://nexus.syncfusioninternal.com/repository/ej2-angular-hotfix/@syncfusion/ej2-angular-notifications/-/ej2-angular-notifications-27.1.48.tgz", "_shasum": "050b433acb488d59005152658d7d54c624b574d8", "_spec": "@syncfusion/ej2-angular-notifications@*", "_where": "/jenkins/workspace/elease-automation_release_27.1.1/ivypackages/included", "author": {"name": "Syncfusion Inc."}, "bugs": {"url": "https://github.com/syncfusion/ej2-angular-ui-components/issues"}, "bundleDependencies": false, "dependencies": {"@syncfusion/ej2-angular-base": "~27.1.50", "@syncfusion/ej2-base": "~27.1.50", "@syncfusion/ej2-notifications": "27.1.50"}, "deprecated": false, "description": "A package of Essential JS 2 notification components such as Toast and Badge which used to notify important information to end-users. for Angular", "es2020": "fesm2020/syncfusion-ej2-angular-notifications.mjs", "esm2020": "esm2020/syncfusion-ej2-angular-notifications.mjs", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./syncfusion-ej2-angular-notifications.d.ts", "esm2020": "./esm2020/syncfusion-ej2-angular-notifications.mjs", "es2020": "./fesm2020/syncfusion-ej2-angular-notifications.mjs", "es2015": "./fesm2015/syncfusion-ej2-angular-notifications.mjs", "node": "./fesm2015/syncfusion-ej2-angular-notifications.mjs", "default": "./fesm2020/syncfusion-ej2-angular-notifications.mjs"}}, "fesm2015": "fesm2015/syncfusion-ej2-angular-notifications.mjs", "fesm2020": "fesm2020/syncfusion-ej2-angular-notifications.mjs", "homepage": "https://www.syncfusion.com/angular-components", "keywords": ["angular", "ng", "ng-template", "ej2-ng-notifications", "ng-notifications", "ng-toast", "angular-toaster", "angular-template", "ej2-angular-notifications", "angular-notifications", "angular-toast", "angular-toaster", "ng-message", "angular-message", "ng-skeleton", "angular-skeleton"], "license": "SEE LICENSE IN license", "module": "fesm2015/syncfusion-ej2-angular-notifications.mjs", "name": "@syncfusion/ej2-angular-notifications", "repository": {"type": "git", "url": "git+https://github.com/syncfusion/ej2-angular-ui-components.git"}, "schematics": "./schematics/collection.json", "sideEffects": false, "typings": "syncfusion-ej2-angular-notifications.d.ts", "version": "27.1.50"}