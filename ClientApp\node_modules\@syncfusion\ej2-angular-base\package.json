{"_from": "@syncfusion/ej2-angular-base@*", "_id": "@syncfusion/ej2-angular-base@27.1.48", "_inBundle": false, "_integrity": "sha512-YEnAAx9GwXqW0PGhnNNlAb3wce0A1Cj2MZ49a/CYODis1+fiP8SxyGMrfXDfMm2zOzs1mh1eCsOOJdwWP0jE3g==", "_location": "/@syncfusion/ej2-angular-base", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@syncfusion/ej2-angular-base@*", "name": "@syncfusion/ej2-angular-base", "escapedName": "@syncfusion%2fej2-angular-base", "scope": "@syncfusion", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/", "/@syncfusion/ej2-angular-barcode-generator", "/@syncfusion/ej2-angular-buttons", "/@syncfusion/ej2-angular-calendars", "/@syncfusion/ej2-angular-charts", "/@syncfusion/ej2-angular-circulargauge", "/@syncfusion/ej2-angular-diagrams", "/@syncfusion/ej2-angular-documenteditor", "/@syncfusion/ej2-angular-dropdowns", "/@syncfusion/ej2-angular-filemanager", "/@syncfusion/ej2-angular-gantt", "/@syncfusion/ej2-angular-grids", "/@syncfusion/ej2-angular-heatmap", "/@syncfusion/ej2-angular-image-editor", "/@syncfusion/ej2-angular-inplace-editor", "/@syncfusion/ej2-angular-inputs", "/@syncfusion/ej2-angular-interactive-chat", "/@syncfusion/ej2-angular-kanban", "/@syncfusion/ej2-angular-layouts", "/@syncfusion/ej2-angular-lineargauge", "/@syncfusion/ej2-angular-lists", "/@syncfusion/ej2-angular-maps", "/@syncfusion/ej2-angular-multicolumn-combobox", "/@syncfusion/ej2-angular-navigations", "/@syncfusion/ej2-angular-notifications", "/@syncfusion/ej2-angular-pdfviewer", "/@syncfusion/ej2-angular-pivotview", "/@syncfusion/ej2-angular-popups", "/@syncfusion/ej2-angular-progressbar", "/@syncfusion/ej2-angular-querybuilder", "/@syncfusion/ej2-angular-ribbon", "/@syncfusion/ej2-angular-richtexteditor", "/@syncfusion/ej2-angular-schedule", "/@syncfusion/ej2-angular-splitbuttons", "/@syncfusion/ej2-angular-spreadsheet", "/@syncfusion/ej2-angular-treegrid", "/@syncfusion/ej2-angular-treemap"], "_resolved": "https://nexus.syncfusioninternal.com/repository/ej2-hotfix-new/@syncfusion/ej2-angular-base/-/ej2-angular-base-27.1.48.tgz", "_shasum": "bb84442b0631aa89afa8d8e6421c5abcbbba6250", "_spec": "@syncfusion/ej2-angular-base@*", "_where": "/jenkins/workspace/elease-automation_release_27.1.1/packages/included", "author": {"name": "Syncfusion Inc."}, "bugs": {"url": "https://github.com/syncfusion/ej2-angular-ui-components/issues"}, "bundleDependencies": false, "dependencies": {"@syncfusion/ej2-base": "~27.1.50", "@syncfusion/ej2-icons": "~27.1.50"}, "deprecated": false, "description": "A common package of Essential JS 2 base Angular libraries, methods and class definitions", "devDependencies": {}, "homepage": "https://www.syncfusion.com/angular-components", "keywords": ["ej2", "syncfusion", "web-components", "ej2-angular-base", "Angular DevKit", "angular", "ng"], "license": "SEE LICENSE IN license", "main": "./dist/ej2-angular-base.umd.min.js", "module": "./index.js", "name": "@syncfusion/ej2-angular-base", "repository": {"type": "git", "url": "git+https://github.com/syncfusion/ej2-angular-ui-components.git"}, "schematics": "./schematics/collection.json", "scripts": {"postinstall": "node ./postinstall.js"}, "typings": "index.d.ts", "version": "27.1.50", "sideEffects": true}