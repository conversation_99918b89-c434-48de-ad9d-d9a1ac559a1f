{"_from": "@syncfusion/ej2-pdf-export@*", "_id": "@syncfusion/ej2-pdf-export@27.1.50", "_inBundle": false, "_integrity": "sha512-Pj0GRZZUgERUxCJxCmok/GOQtYlIeYHXs5/HQ9ufJS/Nao/qCpuDBrEX1V0k2f4QYKhxVGejqUJ7tBDI8eT2uQ==", "_location": "/@syncfusion/ej2-pdf-export", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@syncfusion/ej2-pdf-export@*", "name": "@syncfusion/ej2-pdf-export", "escapedName": "@syncfusion%2fej2-pdf-export", "scope": "@syncfusion", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/", "/@syncfusion/ej2", "/@syncfusion/ej2-charts", "/@syncfusion/ej2-circulargauge", "/@syncfusion/ej2-grids", "/@syncfusion/ej2-lineargauge", "/@syncfusion/ej2-maps", "/@syncfusion/ej2-pivotview", "/@syncfusion/ej2-treemap"], "_resolved": "https://nexus.syncfusioninternal.com/repository/ej2-hotfix-new/@syncfusion/ej2-pdf-export/-/ej2-pdf-export-27.1.50.tgz", "_shasum": "4bf75c8d54ba7e30ed495e3d466de79be2196d3e", "_spec": "@syncfusion/ej2-pdf-export@*", "_where": "/jenkins/workspace/elease-automation_release_27.1.1/packages/included", "author": {"name": "Syncfusion Inc."}, "bundleDependencies": false, "dependencies": {"@syncfusion/ej2-compression": "~27.1.50"}, "deprecated": false, "description": "Syncfusion TypeScript Component", "devDependencies": {}, "es2015": "./dist/es6/ej2-pdf-export.es5.js", "license": "SEE LICENSE IN license", "main": "./dist/ej2-pdf-export.umd.min.js", "module": "./index.js", "name": "@syncfusion/ej2-pdf-export", "typings": "index.d.ts", "version": "27.1.51", "sideEffects": false, "homepage": "https://www.syncfusion.com/javascript-ui-controls"}