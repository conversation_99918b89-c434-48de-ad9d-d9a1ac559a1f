{"_from": "@syncfusion/ej2-angular-popups@*", "_id": "@syncfusion/ej2-angular-popups@27.1.55", "_inBundle": false, "_integrity": "sha512-oy5BNwqkZENpOH+eZiS42VAJRmpKcb4OtjlPf0vP0KNormhoDp4/i2dYvPWgj36Sb1WCVXAexs1gcIPFnQuw8A==", "_location": "/@syncfusion/ej2-angular-popups", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@syncfusion/ej2-angular-popups@*", "name": "@syncfusion/ej2-angular-popups", "escapedName": "@syncfusion%2fej2-angular-popups", "scope": "@syncfusion", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/"], "_resolved": "https://nexus.syncfusioninternal.com/repository/ej2-angular-hotfix/@syncfusion/ej2-angular-popups/-/ej2-angular-popups-27.1.55.tgz", "_shasum": "7c4e0174ea0d6e8738f19a7bad6fea975bcab103", "_spec": "@syncfusion/ej2-angular-popups@*", "_where": "/jenkins/workspace/elease-automation_release_27.1.1/ivypackages/included", "author": {"name": "Syncfusion Inc."}, "bugs": {"url": "https://github.com/syncfusion/ej2-angular-ui-components/issues"}, "bundleDependencies": false, "dependencies": {"@syncfusion/ej2-angular-base": "~27.1.50", "@syncfusion/ej2-base": "~27.1.55", "@syncfusion/ej2-popups": "27.1.56"}, "deprecated": false, "description": "A package of Essential JS 2 popup components such as Dialog and Tooltip that is used to display information or messages in separate pop-ups. for Angular", "es2020": "fesm2020/syncfusion-ej2-angular-popups.mjs", "esm2020": "esm2020/syncfusion-ej2-angular-popups.mjs", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./syncfusion-ej2-angular-popups.d.ts", "esm2020": "./esm2020/syncfusion-ej2-angular-popups.mjs", "es2020": "./fesm2020/syncfusion-ej2-angular-popups.mjs", "es2015": "./fesm2015/syncfusion-ej2-angular-popups.mjs", "node": "./fesm2015/syncfusion-ej2-angular-popups.mjs", "default": "./fesm2020/syncfusion-ej2-angular-popups.mjs"}}, "fesm2015": "fesm2015/syncfusion-ej2-angular-popups.mjs", "fesm2020": "fesm2020/syncfusion-ej2-angular-popups.mjs", "homepage": "https://www.syncfusion.com/angular-components", "keywords": ["angular", "ng", "ng-dialog", "angular-dialog", "ng-tooltip", "angular-tooltip", "ng-spinner", "angular-spinner", "ng-modal", "angular-modal", "ng-alert", "angular-alert", "ng-popup", "angular-popup", "ng-loading-indicator", "angular-loading-indicator", "ng-waiting-popup", "angular-waiting-popup", "ng-loader", "ng-busy-indicator", "ng-waitingfor-loader", "angular-loader", "angular-busy-indicator", "angular-waitingfor-loader", "ng-template-tooltip", "ng-positional-tooltip", "ng-ajax-tooltip", "ng-tooltip -component", "angular-template-tooltip", "angular-positional-tooltip", "angular-ajax-tooltip", "angular-tooltip-component"], "license": "SEE LICENSE IN license", "module": "fesm2015/syncfusion-ej2-angular-popups.mjs", "name": "@syncfusion/ej2-angular-popups", "repository": {"type": "git", "url": "git+https://github.com/syncfusion/ej2-angular-ui-components.git"}, "schematics": "./schematics/collection.json", "sideEffects": false, "typings": "syncfusion-ej2-angular-popups.d.ts", "version": "27.1.56"}