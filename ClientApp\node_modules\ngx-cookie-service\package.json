{"name": "ngx-cookie-service", "description": "Angular cookie service", "version": "18.0.0", "license": "MIT", "author": "<PERSON><PERSON> <<EMAIL>>", "keywords": ["angular", "angular2", "angular4", "angular5", "angular-2", "angular-4", "angular-5", "angular-6", "angular-7", "angular-8", "angular-9", "angular-10", "angular-11", "angular-12", "angular-13", "angular-14", "angular-15", "angular-16", "angular-17", "angular-18", "ivy", "ivy-compatible", "ivy-compilation", "ngx", "ng2", "ng", "service", "angular-service", "cookie-service", "cookie", "cookies"], "contributors": [{"name": "<PERSON><PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "flakolefluk"}, {"name": "mattbanks"}, {"name": "DBaker85"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "IceBreakerG"}], "repository": {"type": "git", "url": "https://github.com/stevermeister/ngx-cookie-service.git"}, "bugs": {"url": "https://github.com/stevermeister/ngx-cookie-service/issues", "email": "<EMAIL>"}, "peerDependencies": {"@angular/common": "^18.0.0-rc.0", "@angular/core": "^18.0.0-rc.0"}, "dependencies": {"tslib": "^2.6.2"}, "resolve": {"fallback": {"path": false, "http": false, "stream": false, "util": false}}, "module": "fesm2022/ngx-cookie-service.mjs", "typings": "index.d.ts", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "esm2022": "./esm2022/ngx-cookie-service.mjs", "esm": "./esm2022/ngx-cookie-service.mjs", "default": "./fesm2022/ngx-cookie-service.mjs"}}, "sideEffects": false}