{"_from": "@syncfusion/ej2-calendars@*", "_id": "@syncfusion/ej2-calendars@27.1.51", "_inBundle": false, "_integrity": "sha512-46rnTrdhdnUUIXd6IlylzsnHHsihlRv8lSTBeRTHYrRfPyW+sR0HQtqh2Sy79f+hUyrTz0NN4o4irXimzo8w7A==", "_location": "/@syncfusion/ej2-calendars", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@syncfusion/ej2-calendars@*", "name": "@syncfusion/ej2-calendars", "escapedName": "@syncfusion%2fej2-calendars", "scope": "@syncfusion", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/", "/@syncfusion/ej2", "/@syncfusion/ej2-angular-calendars", "/@syncfusion/ej2-charts", "/@syncfusion/ej2-documenteditor", "/@syncfusion/ej2-gantt", "/@syncfusion/ej2-grids", "/@syncfusion/ej2-inplace-editor", "/@syncfusion/ej2-pivotview", "/@syncfusion/ej2-querybuilder", "/@syncfusion/ej2-react-calendars", "/@syncfusion/ej2-schedule", "/@syncfusion/ej2-vue-calendars"], "_resolved": "https://nexus.syncfusioninternal.com/repository/ej2-hotfix-new/@syncfusion/ej2-calendars/-/ej2-calendars-27.1.51.tgz", "_shasum": "02916eff14ef8c43e29a4616e5fe7507f78ff790", "_spec": "@syncfusion/ej2-calendars@*", "_where": "/jenkins/workspace/elease-automation_release_27.1.1/packages/included", "author": {"name": "Syncfusion Inc."}, "bugs": {"url": "https://github.com/syncfusion/ej2-javascript-ui-controls/issues"}, "bundleDependencies": false, "dependencies": {"@syncfusion/ej2-base": "~27.1.52", "@syncfusion/ej2-buttons": "~27.1.51", "@syncfusion/ej2-inputs": "~27.1.50", "@syncfusion/ej2-lists": "~27.1.50", "@syncfusion/ej2-popups": "~27.1.50"}, "deprecated": false, "description": "A complete package of date or time components with built-in features such as date formatting, inline editing, multiple (range) selection, range restriction, month and year selection, strict mode, and globalization.", "devDependencies": {}, "es2015": "./dist/es6/ej2-calendars.es5.js", "homepage": "https://www.syncfusion.com/javascript-ui-controls", "keywords": ["ej2", "syncfusion", "ej2-calendars", "web-components", "JavaScript", "TypeScript", "calendar", "date", "culture", "month", "year", "decade", "timepicker", "stepranges", "time"], "license": "SEE LICENSE IN license", "main": "./dist/ej2-calendars.umd.min.js", "module": "./index.js", "name": "@syncfusion/ej2-calendars", "repository": {"type": "git", "url": "git+https://github.com/syncfusion/ej2-javascript-ui-controls.git"}, "typings": "index.d.ts", "version": "27.1.52", "sideEffects": false}