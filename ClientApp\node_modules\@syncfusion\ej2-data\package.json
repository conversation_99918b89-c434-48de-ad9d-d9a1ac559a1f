{"_from": "@syncfusion/ej2-data@*", "_id": "@syncfusion/ej2-data@27.1.51", "_inBundle": false, "_integrity": "sha512-P06LrpbmrYAvgxoINOHZiZ28tLBbWwTw5xqWny9tog0cllTnA9N7KgpL3qRULpq5nPQNwKtOBLyZZcdcNIdaPA==", "_location": "/@syncfusion/ej2-data", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@syncfusion/ej2-data@*", "name": "@syncfusion/ej2-data", "escapedName": "@syncfusion%2fej2-data", "scope": "@syncfusion", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/", "/@syncfusion/ej2", "/@syncfusion/ej2-barcode-generator", "/@syncfusion/ej2-charts", "/@syncfusion/ej2-diagrams", "/@syncfusion/ej2-drawings", "/@syncfusion/ej2-dropdowns", "/@syncfusion/ej2-filemanager", "/@syncfusion/ej2-gantt", "/@syncfusion/ej2-grids", "/@syncfusion/ej2-heatmap", "/@syncfusion/ej2-inplace-editor", "/@syncfusion/ej2-kanban", "/@syncfusion/ej2-lists", "/@syncfusion/ej2-maps", "/@syncfusion/ej2-navigations", "/@syncfusion/ej2-pdfviewer", "/@syncfusion/ej2-pivotview", "/@syncfusion/ej2-progressbar", "/@syncfusion/ej2-schedule", "/@syncfusion/ej2-treegrid", "/@syncfusion/ej2-treemap"], "_resolved": "https://nexus.syncfusioninternal.com/repository/ej2-hotfix-new/@syncfusion/ej2-data/-/ej2-data-27.1.51.tgz", "_shasum": "d4d47017e80db1bf6f593e27127c127f9c159901", "_spec": "@syncfusion/ej2-data@*", "_where": "/jenkins/workspace/elease-automation_release_27.1.1/packages/included", "author": {"name": "Syncfusion Inc."}, "bundleDependencies": false, "dependencies": {"@syncfusion/ej2-base": "~27.1.52"}, "deprecated": false, "description": "Essential JS 2 DataManager", "devDependencies": {}, "es2015": "./dist/es6/ej2-data.es5.js", "license": "SEE LICENSE IN license", "main": "./dist/ej2-data.umd.min.js", "module": "./index.js", "name": "@syncfusion/ej2-data", "typings": "index.d.ts", "version": "27.1.52", "sideEffects": false, "homepage": "https://www.syncfusion.com/javascript-ui-controls"}