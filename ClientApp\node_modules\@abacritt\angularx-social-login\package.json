{"name": "@abacritt/angularx-social-login", "version": "2.3.0", "description": "Social login and authentication module for Angular 18. Supports authentication with Google, Facebook, Amazon, and VK. Can be extended to other providers also.", "repository": {"type": "git", "url": "git+https://github.com/abacritt/angularx-social-login.git"}, "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "keywords": ["angular", "angular18", "angular-social-login", "social-authentication", "social-login", "google-authentication", "facebook-authentication", "amazon-authentication"], "license": "MIT", "bugs": {"url": "https://github.com/abacritt/angularx-social-login/issues"}, "dependencies": {"tslib": ">=2.6.3"}, "peerDependencies": {"@angular/common": ">=18.0.1", "@angular/core": ">=18.0.1"}, "homepage": "https://github.com/abacritt/angularx-social-login#readme", "main": "bundles/abacrit-angularx-social-login.umd.js", "module": "fesm2022/abacritt-angularx-social-login.mjs", "typings": "index.d.ts", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "esm2022": "./esm2022/abacritt-angularx-social-login.mjs", "esm": "./esm2022/abacritt-angularx-social-login.mjs", "default": "./fesm2022/abacritt-angularx-social-login.mjs"}}, "sideEffects": false}