{"_from": "@syncfusion/ej2-splitbuttons@*", "_id": "@syncfusion/ej2-splitbuttons@27.1.50", "_inBundle": false, "_integrity": "sha512-RzgMZ/5XxQlipWvdCKZv+jumz2kSyzwgp/h07m/rHyltisnbMti3IoZPmIafpR7eIjjbDF+dvPDB4edFsNx3hg==", "_location": "/@syncfusion/ej2-splitbuttons", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@syncfusion/ej2-splitbuttons@*", "name": "@syncfusion/ej2-splitbuttons", "escapedName": "@syncfusion%2fej2-splitbuttons", "scope": "@syncfusion", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/", "/@syncfusion/ej2", "/@syncfusion/ej2-angular-splitbuttons", "/@syncfusion/ej2-documenteditor", "/@syncfusion/ej2-filemanager", "/@syncfusion/ej2-grids", "/@syncfusion/ej2-image-editor", "/@syncfusion/ej2-inplace-editor", "/@syncfusion/ej2-inputs", "/@syncfusion/ej2-pivotview", "/@syncfusion/ej2-querybuilder", "/@syncfusion/ej2-react-splitbuttons", "/@syncfusion/ej2-ribbon", "/@syncfusion/ej2-richtexteditor", "/@syncfusion/ej2-vue-splitbuttons"], "_resolved": "https://nexus.syncfusioninternal.com/repository/ej2-hotfix-new/@syncfusion/ej2-splitbuttons/-/ej2-splitbuttons-27.1.50.tgz", "_shasum": "692f462aa5de59fdf6d19e3ee2dc0bf6c84c87c8", "_spec": "@syncfusion/ej2-splitbuttons@*", "_where": "/jenkins/workspace/elease-automation_release_27.1.1/packages/included", "author": {"name": "Syncfusion Inc."}, "bugs": {"url": "https://github.com/syncfusion/ej2-javascript-ui-controls/issues"}, "bundleDependencies": false, "dependencies": {"@syncfusion/ej2-base": "~27.1.55", "@syncfusion/ej2-popups": "~27.1.56"}, "deprecated": false, "description": "A package of feature-rich Essential JS 2 components such as DropDownButton, SplitButton, ProgressButton and ButtonGroup.", "devDependencies": {}, "es2015": "./dist/es6/ej2-splitbuttons.es5.js", "homepage": "https://www.syncfusion.com/javascript-ui-controls", "keywords": ["ej2", "syncfusion", "ej2-splitbuttons", "ej2 splitbutton", "ej2 dropdownbutton", "ej2 buttongroup", "ej2 progress button", "JavaScript", "TypeScript", "split button", "splitbutton", "dropdown", "dropdown button", "drop-down button", "dropdownbutton", "dropdown popup", "button group", "buttongroup", "group button", "progress", "spinner", "progress button", "progress indicator", "spinbutton", "spin button"], "license": "SEE LICENSE IN license", "main": "./dist/ej2-splitbuttons.umd.min.js", "module": "./index.js", "name": "@syncfusion/ej2-splitbuttons", "repository": {"type": "git", "url": "git+https://github.com/syncfusion/ej2-javascript-ui-controls.git"}, "typings": "index.d.ts", "version": "27.1.56", "sideEffects": false}