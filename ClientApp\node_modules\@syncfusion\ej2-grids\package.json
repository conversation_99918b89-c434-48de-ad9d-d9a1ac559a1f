{"_from": "@syncfusion/ej2-grids@*", "_id": "@syncfusion/ej2-grids@27.1.51", "_inBundle": false, "_integrity": "sha512-W1KVR3znymMWffyL098dhYFYuWfajcY8AOgFlehosC/Oyl1/naAxeqhKf+QUPS/sJYKZJn/5Bili/ad8UALYew==", "_location": "/@syncfusion/ej2-grids", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@syncfusion/ej2-grids@*", "name": "@syncfusion/ej2-grids", "escapedName": "@syncfusion%2fej2-grids", "scope": "@syncfusion", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/", "/@syncfusion/ej2", "/@syncfusion/ej2-angular-grids", "/@syncfusion/ej2-filemanager", "/@syncfusion/ej2-gantt", "/@syncfusion/ej2-multicolumn-combobox", "/@syncfusion/ej2-pivotview", "/@syncfusion/ej2-react-grids", "/@syncfusion/ej2-spreadsheet", "/@syncfusion/ej2-treegrid", "/@syncfusion/ej2-vue-grids"], "_resolved": "https://nexus.syncfusioninternal.com/repository/ej2-hotfix-new/@syncfusion/ej2-grids/-/ej2-grids-27.1.51.tgz", "_shasum": "61c1886dda3af3b730f900205d60cc4d23aba9dd", "_spec": "@syncfusion/ej2-grids@*", "_where": "/jenkins/workspace/elease-automation_release_27.1.1/packages/included", "author": {"name": "Syncfusion Inc."}, "bundleDependencies": false, "dependencies": {"@syncfusion/ej2-base": "~27.1.52", "@syncfusion/ej2-buttons": "~27.1.51", "@syncfusion/ej2-calendars": "~27.1.52", "@syncfusion/ej2-compression": "~27.1.50", "@syncfusion/ej2-data": "~27.1.52", "@syncfusion/ej2-dropdowns": "~27.1.52", "@syncfusion/ej2-excel-export": "~27.1.50", "@syncfusion/ej2-file-utils": "~27.1.50", "@syncfusion/ej2-inputs": "~27.1.50", "@syncfusion/ej2-lists": "~27.1.50", "@syncfusion/ej2-navigations": "~27.1.52", "@syncfusion/ej2-notifications": "~27.1.50", "@syncfusion/ej2-pdf-export": "~27.1.51", "@syncfusion/ej2-popups": "~27.1.50", "@syncfusion/ej2-splitbuttons": "~27.1.50"}, "deprecated": false, "description": "Feature-rich JavaScript datagrid (datatable) control with built-in support for editing, filtering, grouping, paging, sorting, and exporting to Excel.", "devDependencies": {}, "es2015": "./dist/es6/ej2-grids.es5.js", "keywords": ["ej2", "syncfusion", "ej2-grids", "web-components", "JavaScript", "TypeScript", "grid", "data", "table"], "license": "SEE LICENSE IN license", "main": "./dist/ej2-grids.umd.min.js", "module": "./index.js", "name": "@syncfusion/ej2-grids", "repository": {"type": "git", "url": "https://github.com/syncfusion/ej2-javascript-ui-controls/tree/master/controls/grids"}, "typings": "index.d.ts", "version": "27.1.52", "sideEffects": false, "homepage": "https://www.syncfusion.com/javascript-ui-controls"}