{"_from": "@syncfusion/ej2-lists@*", "_id": "@syncfusion/ej2-lists@27.1.48", "_inBundle": false, "_integrity": "sha512-VLAcKxnQ8AG3M62SqmA0FKuQD+xlK8DBndql8mlKuI9e5747NZ+HDC304pssEjOq8JKYoXO5fuPmPx2mbRREGw==", "_location": "/@syncfusion/ej2-lists", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@syncfusion/ej2-lists@*", "name": "@syncfusion/ej2-lists", "escapedName": "@syncfusion%2fej2-lists", "scope": "@syncfusion", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/", "/@syncfusion/ej2", "/@syncfusion/ej2-angular-lists", "/@syncfusion/ej2-calendars", "/@syncfusion/ej2-diagrams", "/@syncfusion/ej2-dropdowns", "/@syncfusion/ej2-filemanager", "/@syncfusion/ej2-gantt", "/@syncfusion/ej2-grids", "/@syncfusion/ej2-inplace-editor", "/@syncfusion/ej2-navigations", "/@syncfusion/ej2-pdfviewer", "/@syncfusion/ej2-pivotview", "/@syncfusion/ej2-react-lists", "/@syncfusion/ej2-ribbon", "/@syncfusion/ej2-schedule", "/@syncfusion/ej2-vue-lists"], "_resolved": "https://nexus.syncfusioninternal.com/repository/ej2-hotfix-new/@syncfusion/ej2-lists/-/ej2-lists-27.1.48.tgz", "_shasum": "c65d2f491f70e1f7c908e25f83419ec1e6862386", "_spec": "@syncfusion/ej2-lists@*", "_where": "/jenkins/workspace/elease-automation_release_27.1.1/packages/included", "author": {"name": "Syncfusion Inc."}, "bundleDependencies": false, "dependencies": {"@syncfusion/ej2-base": "~27.1.50", "@syncfusion/ej2-buttons": "~27.1.50", "@syncfusion/ej2-data": "~27.1.50", "@syncfusion/ej2-popups": "~27.1.50"}, "deprecated": false, "description": "The listview control allows you to select an item or multiple items from a list-like interface and represents the data in interactive hierarchical structure across different layouts or views.", "devDependencies": {}, "es2015": "./dist/es6/ej2-lists.es5.js", "keywords": ["ej2", "Syncfusion", "web components", "virtualization", "group list", "check list", "listview with checkbox", "listview template", "material list", "ui listview", "remote listview", "nested view", "data list", "infinite list", "sort", "checkbox", "todo", "list widget", "list component", "mobile list", "list gestures", "es6 list", "dynamic list"], "license": "SEE LICENSE IN license", "main": "./dist/ej2-lists.umd.min.js", "module": "./index.js", "name": "@syncfusion/ej2-lists", "repository": {"type": "git", "url": "https://github.com/syncfusion/ej2-javascript-ui-controls/tree/master/controls/lists"}, "typings": "index.d.ts", "version": "27.1.50", "sideEffects": false, "homepage": "https://www.syncfusion.com/javascript-ui-controls"}