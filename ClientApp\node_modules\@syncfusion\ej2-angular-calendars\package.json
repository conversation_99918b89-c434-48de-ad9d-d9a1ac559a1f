{"_from": "@syncfusion/ej2-angular-calendars@*", "_id": "@syncfusion/ej2-angular-calendars@27.1.51", "_inBundle": false, "_integrity": "sha512-EGdwI1HtYLTClz9d1HNorYWPHVE/pkg68DRYekrq6eAQ+OADwyghBoFwVDvvhxdHUYzmpfNGD6ydqDCjAw+fXQ==", "_location": "/@syncfusion/ej2-angular-calendars", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@syncfusion/ej2-angular-calendars@*", "name": "@syncfusion/ej2-angular-calendars", "escapedName": "@syncfusion%2fej2-angular-calendars", "scope": "@syncfusion", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/"], "_resolved": "https://nexus.syncfusioninternal.com/repository/ej2-angular-hotfix/@syncfusion/ej2-angular-calendars/-/ej2-angular-calendars-27.1.51.tgz", "_shasum": "c1fed12700c1882b75c8271a20fadaea7c25caac", "_spec": "@syncfusion/ej2-angular-calendars@*", "_where": "/jenkins/workspace/elease-automation_release_27.1.1/ivypackages/included", "author": {"name": "Syncfusion Inc."}, "bugs": {"url": "https://github.com/syncfusion/ej2-angular-ui-components/issues"}, "bundleDependencies": false, "dependencies": {"@syncfusion/ej2-angular-base": "~27.1.50", "@syncfusion/ej2-base": "~27.1.52", "@syncfusion/ej2-calendars": "27.1.52"}, "deprecated": false, "description": "A complete package of date or time components with built-in features such as date formatting, inline editing, multiple (range) selection, range restriction, month and year selection, strict mode, and globalization. for Angular", "es2020": "fesm2020/syncfusion-ej2-angular-calendars.mjs", "esm2020": "esm2020/syncfusion-ej2-angular-calendars.mjs", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./syncfusion-ej2-angular-calendars.d.ts", "esm2020": "./esm2020/syncfusion-ej2-angular-calendars.mjs", "es2020": "./fesm2020/syncfusion-ej2-angular-calendars.mjs", "es2015": "./fesm2015/syncfusion-ej2-angular-calendars.mjs", "node": "./fesm2015/syncfusion-ej2-angular-calendars.mjs", "default": "./fesm2020/syncfusion-ej2-angular-calendars.mjs"}}, "fesm2015": "fesm2015/syncfusion-ej2-angular-calendars.mjs", "fesm2020": "fesm2020/syncfusion-ej2-angular-calendars.mjs", "homepage": "https://www.syncfusion.com/angular-components", "keywords": ["angular", "ng", "ng-calendars", "ej2-ng-calendars"], "license": "SEE LICENSE IN license", "module": "fesm2015/syncfusion-ej2-angular-calendars.mjs", "name": "@syncfusion/ej2-angular-calendars", "repository": {"type": "git", "url": "git+https://github.com/syncfusion/ej2-angular-ui-components.git"}, "schematics": "./schematics/collection.json", "sideEffects": false, "typings": "syncfusion-ej2-angular-calendars.d.ts", "version": "27.1.52"}