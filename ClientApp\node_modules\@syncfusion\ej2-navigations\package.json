{"_from": "@syncfusion/ej2-navigations@*", "_id": "@syncfusion/ej2-navigations@27.1.51", "_inBundle": false, "_integrity": "sha512-LVFCO84fnFIcfyc0BIdo23SF1RwGWdfaXoIx6iZAYCuXhnEH1+9JEUqLKuomQyqBiD+oehVGZjL/HD8ZrX5iqA==", "_location": "/@syncfusion/ej2-navigations", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@syncfusion/ej2-navigations@*", "name": "@syncfusion/ej2-navigations", "escapedName": "@syncfusion%2fej2-navigations", "scope": "@syncfusion", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/", "/@syncfusion/ej2", "/@syncfusion/ej2-angular-navigations", "/@syncfusion/ej2-charts", "/@syncfusion/ej2-diagrams", "/@syncfusion/ej2-documenteditor", "/@syncfusion/ej2-dropdowns", "/@syncfusion/ej2-filemanager", "/@syncfusion/ej2-gantt", "/@syncfusion/ej2-grids", "/@syncfusion/ej2-image-editor", "/@syncfusion/ej2-inplace-editor", "/@syncfusion/ej2-interactive-chat", "/@syncfusion/ej2-kanban", "/@syncfusion/ej2-pdfviewer", "/@syncfusion/ej2-pivotview", "/@syncfusion/ej2-react-navigations", "/@syncfusion/ej2-ribbon", "/@syncfusion/ej2-richtexteditor", "/@syncfusion/ej2-schedule", "/@syncfusion/ej2-spreadsheet", "/@syncfusion/ej2-vue-navigations"], "_resolved": "https://nexus.syncfusioninternal.com/repository/ej2-hotfix-new/@syncfusion/ej2-navigations/-/ej2-navigations-27.1.51.tgz", "_shasum": "e7a5ebcd6a85807bb494750a786f4ea8e08f827d", "_spec": "@syncfusion/ej2-navigations@*", "_where": "/jenkins/workspace/elease-automation_release_27.1.1/packages/included", "author": {"name": "Syncfusion Inc."}, "bugs": {"url": "https://github.com/syncfusion/ej2-javascript-ui-controls/issues"}, "bundleDependencies": false, "dependencies": {"@syncfusion/ej2-base": "~27.1.52", "@syncfusion/ej2-buttons": "~27.1.51", "@syncfusion/ej2-data": "~27.1.52", "@syncfusion/ej2-inputs": "~27.1.50", "@syncfusion/ej2-lists": "~27.1.50", "@syncfusion/ej2-popups": "~27.1.50"}, "deprecated": false, "description": "A package of Essential JS 2 navigation components such as Tree-view, Tab, Toolbar, Context-menu, and Accordion which is used to navigate from one page to another", "devDependencies": {}, "es2015": "./dist/es6/ej2-navigations.es5.js", "homepage": "https://www.syncfusion.com/javascript-ui-controls", "keywords": ["ej2", "syncfusion", "web-components", "javascript", "typescript", "model", "toolbar", "horizontal-scroll", "ribbon", "navbar", "navigation-bar", "toolbar-customization", "scrollable-toolbar", "toolbar-popup", "multirow-toolbar", "extended-toolbar", "overflow", "command-buttons", "accordion", "panelbar", "ejTab", "tab", "tabStrip", "scrollable-tab", "popup-tab", "multirow-tab", "header-orientation", "close-tab", "tab-wizard", "tab-with-menu", "ej<PERSON><PERSON>rdion", "accordion", "accordion-wizard", "multiple-open-panel-bar", "multiple-open-accordion", "panel-bar", "collapsible-panel-bar", "context-menu", "context menu", "contextmenu", "ej2 contextmenu", "treeview", "tree", "ej2-treeview", "tree structure", "hierarchical structure", "tree navigation", "treeview-checkbox", "drag and drop", "tree editing", "load on demand", "accordion tree", "sidebar", "dock size", "showbackdrop", "target", "position", "dock", "gestures", "sticky-sidebar", "fixed", "hamburger", "sidenav", "nav", "hamburger-menu", "side-menu", "navigation-bar", "sticky", "drawer", "slidebar", "slide", "slide-panel", "multiple-sidebar", "slide-navigation", "navigation-drawer", "breadcrumb", "breadcrumb navigation", "breadcrumb trail", "ej2 breadcrumb", "navigation links", "navigational aid", "previous page navigation", "current page hierarchy", "current page location", "path of url", "trace page location", "list of breadcrumb links"], "license": "SEE LICENSE IN license", "main": "./dist/ej2-navigations.umd.min.js", "module": "./index.js", "name": "@syncfusion/ej2-navigations", "repository": {"type": "git", "url": "git+https://github.com/syncfusion/ej2-javascript-ui-controls.git"}, "typings": "index.d.ts", "version": "27.1.52", "sideEffects": false}