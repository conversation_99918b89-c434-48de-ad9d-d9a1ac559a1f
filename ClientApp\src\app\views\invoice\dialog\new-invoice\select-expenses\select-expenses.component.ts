import { IExpensesSelect } from './../../../../../dto/interface/ExpensesSelect.interface';
import { SortGird } from 'app/dto/interface/SortGird.interface';
import { DecimalPipe } from './../../../../../pipes/decimal.pipe';
import { StoreService } from 'app/service/store.service';
import { Component, DestroyRef, inject, Inject, OnDestroy, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { InnoEmptyDataComponent } from 'app/component/inno-empty-data/inno-empty-data.component';
import { InnoFormCheckboxComponent } from 'app/component/inno-form-checkbox/inno-form-checkbox.component';
import { InnoModalFooterComponent } from 'app/component/inno-modal-footer/inno-modal-footer.component';
import { InnoModalWrapperComponent } from 'app/component/inno-modal-wrapper/inno-modal-wrapper.component';
import { InnoSpinomponent } from 'app/component/inno-spin/inno-spin.component';
import { SharedModule } from 'app/module/shared.module';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { InvoiceItem } from '../../../../../dto/interface/invoiceItem.interface';
import { MatTooltip } from '@angular/material/tooltip';
import { AvatarModule } from 'ngx-avatars';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { GetExpenseQueryParam, Parameter } from 'app/dto/interface/queryParameter.interface';
import { Pager, PagerDropDown, PagerModule } from '@syncfusion/ej2-angular-grids';
import { ExpensesService } from 'app/service/expenses.service';
import { calculateGroupedTaxes } from '../../../../../utils/invoice.helper';
Pager.Inject(PagerDropDown);
interface IAddTrackingByClient {
  client: Record<string, any>, // id, name
  project: Record<string, any>, // id, name
  listIdTimeTrackingSelected: InvoiceItem[]
}

@Component({
  selector: 'app-select-expenses',
  templateUrl: './select-expenses.component.html',
  styleUrl: './select-expenses.component.scss',
  standalone: true,
  imports: [
    MatTooltip,
    AvatarModule,
    SharedModule,
    PagerModule,
    FormatNumberPipe,
    DecimalPipe,
    InnoModalWrapperComponent,
    InnoModalFooterComponent,
    InnoFormCheckboxComponent,
    InnoSpinomponent,
    InnoEmptyDataComponent,
  ]
})
export class SelectExpensesComponent implements OnInit, OnDestroy {
  public sort: SortGird
  today = new Date();
  public totalPages = 1;
  public currentPage: number = 1
  public pageSizes = [10, 20, 50, 100];
  public pageSizesDefault: number = 20
  public title: string = ''
  public clientName: string = ''
  public projectName: string = ''
  public listExpenses: IExpensesSelect[] = []
  public listIndexInvoiceSelected: number[] = []
  public isFetchingProject: boolean = false
  listProjectId: any[] = []
  sortDirection: 'Ascending' | 'Descending' = 'Ascending';
  sortColumn: string = ''

  private destroyRef = inject(DestroyRef);
  private expensesService = inject(ExpensesService)
  public _storeService = inject(StoreService)
  static getComponent(): typeof SelectExpensesComponent {
    return SelectExpensesComponent;
  }

  constructor(
    public dialogRef: MatDialogRef<SelectExpensesComponent>,
    @Inject(MAT_DIALOG_DATA) public data?: IAddTrackingByClient
  ) {
    this.clientName = data?.client?.['clientName'] ?? ''
    this.projectName = data?.project?.['projectName'] ?? ''
    this.title = `Select expenses ${this.clientName}${this.projectName != '' ? ' / ' + this.projectName : ''}`;
  }
  ngOnDestroy(): void {
    this.listProjectId = [];
  }
  handleClose() {
    this.dialogRef.close();
  }
  handleSort(sortDirection: string) {
    const sort = this.sort
    const page = this.currentPage
    const textSearch = ""
    const isGetAll = false
    switch (sortDirection) {
      case 'Descending':
        this.GetAllExpensesUnBillTime({ page, textSearch, isGetAll, sort });
        break;
      case 'Ascending':
        this.GetAllExpensesUnBillTime({ page, textSearch, isGetAll, sort });
        break;
      default:
        break;
    }
  }

  sortDates(column: string) {
    if (this.sortColumn === column) {
      this.sortDirection = this.sortDirection === 'Ascending' ? 'Descending' : 'Ascending';
      this.sort = {
        columnName: this.sortColumn,
        direction: this.sortDirection
      }
      this.handleSort(this.sortDirection)
    } else {
      this.sortColumn = column;
      this.sortDirection = 'Ascending';
      this.sort = {
        columnName: this.sortColumn,
        direction: this.sortDirection
      }
      this.handleSort(this.sortDirection)
    }
  }
  GetAllExpensesUnBillTime(args?: { page?: number, textSearch?: string, isGetAll?: boolean, sort?: any }) {
    const clientId = this.data?.client?.['id']
    const project: any = this.data?.project
    if (!clientId) return;
    if (project) {
      this.listProjectId.push(project.id)
    }

    const params: GetExpenseQueryParam = {
      Page: args?.page ?? 1,
      Search: args?.textSearch ?? '',
      PageSize: this.pageSizesDefault,
      clientId,
      isGetAll: args.isGetAll,
      ...args.sort,
      status: 1,
      projectIds: this.listProjectId
    }

    this.isFetchingProject = true
    this.expensesService.GetAllExpenses(params)
      .pipe(takeUntilDestroyed(this.destroyRef)).subscribe((listResponse: any) => {
        if (listResponse) {
          this.totalPages = listResponse.totalRecords
          const listIdTimeTrackingExist = this.data?.listIdTimeTrackingSelected ?? []
          listResponse = listResponse?.data ? listResponse?.data?.filter((item: any) => !listIdTimeTrackingExist.includes(item.id)) ?? [] : listResponse?.filter((item: any) => !listIdTimeTrackingExist.includes(item.id)) ?? []

          this.listExpenses = listResponse.map((item: any) => {
            return {
              expensesId: item?.id ?? '',
              date: item?.date ?? this.today,
              description: item?.note ?? '',
              total: item?.paidAmount,
              dateSelectItem: item.date,
              clientName: item?.clientName,
              expensesName: item?.expensesName,
              inforUser: item?.inforUser,
              projectName: item?.projectName,
              taxes: []
            }
          })
          this.isFetchingProject = false
          if (args.isGetAll) {
            this.pageSizesDefault = this.totalPages
            this.listIndexInvoiceSelected = this.listExpenses.map((_item, index) => index)
          }
        }
      })

  }
  onPageChange(event: any): void {
    if (event?.newProp?.pageSize) {
      this.pageSizesDefault = event.newProp.pageSize
      const page = this.currentPage
      const textSearch = ""
      const isGetAll = false
      this.sort ? this.handleSort(this.sortDirection) : this.GetAllExpensesUnBillTime({ page, textSearch, isGetAll });
    }
    if (event?.currentPage) {
      this.currentPage = event.currentPage
      const page = this.currentPage
      const textSearch = ""
      const isGetAll = false
      this.sort ? this.handleSort(this.sortDirection) : this.GetAllExpensesUnBillTime({ page, textSearch, isGetAll });
    }
  }

  ngOnInit(): void {
    this.sortDirection = "Ascending"
    this.sortColumn = 'date'
    this.sort = {
      columnName: this.sortColumn,
      direction: this.sortDirection
    }
    this.handleSort(this.sortDirection)
  }

  handleCheckedAll(isChecked: any) {
    if (isChecked) {
      const sort = this.sort
      const page = this.currentPage
      const textSearch = ""
      const isGetAll = true
      this.GetAllExpensesUnBillTime({ page, textSearch, isGetAll, sort });
    } else {
      this.listIndexInvoiceSelected = []
    }
  }

  isCheckedIndex(index: number): boolean {
    return this.listIndexInvoiceSelected.includes(index)
  }

  handleToggleCheckedIndex(index: number) {
    const isChecked = this.isCheckedIndex(index)
    let newListSelected = [...this.listIndexInvoiceSelected]

    if (isChecked) {
      newListSelected = newListSelected.filter(_i => _i !== index)
    } else {
      newListSelected.push(index)
    }
    this.listIndexInvoiceSelected = newListSelected
  }

  totalAmount() {
    const resultTax = calculateGroupedTaxes(this.listExpenses)
    return resultTax.subtotal + resultTax.grandTotalTax ?? 0
  }

  handleCancel() {
    this.dialogRef.close();
  }

  handleSubmit() {
    const listInvoiceItemSelected = this.listIndexInvoiceSelected.map(index => this.listExpenses[index]);
    this.dialogRef.close(listInvoiceItemSelected)

  }

  calculateTotalInvoiceItem(time: string, rate: number) {
    const parts = time.split(":").map(Number);
    let hours = 0, minutes = 0, seconds = 0;
    if (parts.length === 3) {
      [hours, minutes, seconds] = parts; // HH:MM:SS
    } else if (parts.length === 2) {
      [hours, minutes] = parts; // HH:MM (default seconds to 0)
    } else if (parts.length === 1) {
      [hours] = parts; // HH (default minutes and seconds to 0)
    }
    const decimalHours = hours + minutes / 60 + seconds / 3600;
    return decimalHours * rate
  }
}
