{"_from": "@syncfusion/ej2-base@*", "_id": "@syncfusion/ej2-base@27.1.53", "_inBundle": false, "_integrity": "sha512-V8gtssZUy8haZ85F4yICgOI9AymGipVfh7ardoDTzJi9pznS/YBo3XVUqaa5e04dw1hm0RmxPkMBvX0vJWg9nw==", "_location": "/@syncfusion/ej2-base", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@syncfusion/ej2-base@*", "name": "@syncfusion/ej2-base", "escapedName": "@syncfusion%2fej2-base", "scope": "@syncfusion", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/", "/@syncfusion/ej2", "/@syncfusion/ej2-angular-barcode-generator", "/@syncfusion/ej2-angular-base", "/@syncfusion/ej2-angular-calendars", "/@syncfusion/ej2-angular-charts", "/@syncfusion/ej2-angular-diagrams", "/@syncfusion/ej2-angular-documenteditor", "/@syncfusion/ej2-angular-dropdowns", "/@syncfusion/ej2-angular-filemanager", "/@syncfusion/ej2-angular-gantt", "/@syncfusion/ej2-angular-grids", "/@syncfusion/ej2-angular-inputs", "/@syncfusion/ej2-angular-navigations", "/@syncfusion/ej2-angular-pdfviewer", "/@syncfusion/ej2-angular-pivotview", "/@syncfusion/ej2-angular-popups", "/@syncfusion/ej2-angular-querybuilder", "/@syncfusion/ej2-angular-richtexteditor", "/@syncfusion/ej2-angular-schedule", "/@syncfusion/ej2-angular-spreadsheet", "/@syncfusion/ej2-angular-treegrid", "/@syncfusion/ej2-angular-treemap", "/@syncfusion/ej2-barcode-generator", "/@syncfusion/ej2-buttons", "/@syncfusion/ej2-calendars", "/@syncfusion/ej2-charts", "/@syncfusion/ej2-circulargauge", "/@syncfusion/ej2-data", "/@syncfusion/ej2-diagrams", "/@syncfusion/ej2-documenteditor", "/@syncfusion/ej2-drawings", "/@syncfusion/ej2-dropdowns", "/@syncfusion/ej2-excel-export", "/@syncfusion/ej2-filemanager", "/@syncfusion/ej2-gantt", "/@syncfusion/ej2-grids", "/@syncfusion/ej2-heatmap", "/@syncfusion/ej2-image-editor", "/@syncfusion/ej2-inplace-editor", "/@syncfusion/ej2-inputs", "/@syncfusion/ej2-interactive-chat", "/@syncfusion/ej2-kanban", "/@syncfusion/ej2-layouts", "/@syncfusion/ej2-lineargauge", "/@syncfusion/ej2-lists", "/@syncfusion/ej2-maps", "/@syncfusion/ej2-multicolumn-combobox", "/@syncfusion/ej2-navigations", "/@syncfusion/ej2-notifications", "/@syncfusion/ej2-pdf", "/@syncfusion/ej2-pdfviewer", "/@syncfusion/ej2-pivotview", "/@syncfusion/ej2-popups", "/@syncfusion/ej2-progressbar", "/@syncfusion/ej2-querybuilder", "/@syncfusion/ej2-react-barcode-generator", "/@syncfusion/ej2-react-base", "/@syncfusion/ej2-react-calendars", "/@syncfusion/ej2-react-charts", "/@syncfusion/ej2-react-diagrams", "/@syncfusion/ej2-react-documenteditor", "/@syncfusion/ej2-react-dropdowns", "/@syncfusion/ej2-react-filemanager", "/@syncfusion/ej2-react-gantt", "/@syncfusion/ej2-react-grids", "/@syncfusion/ej2-react-inputs", "/@syncfusion/ej2-react-navigations", "/@syncfusion/ej2-react-pdfviewer", "/@syncfusion/ej2-react-pivotview", "/@syncfusion/ej2-react-popups", "/@syncfusion/ej2-react-querybuilder", "/@syncfusion/ej2-react-richtexteditor", "/@syncfusion/ej2-react-schedule", "/@syncfusion/ej2-react-spreadsheet", "/@syncfusion/ej2-react-treegrid", "/@syncfusion/ej2-react-treemap", "/@syncfusion/ej2-ribbon", "/@syncfusion/ej2-richtexteditor", "/@syncfusion/ej2-schedule", "/@syncfusion/ej2-splitbuttons", "/@syncfusion/ej2-spreadsheet", "/@syncfusion/ej2-svg-base", "/@syncfusion/ej2-treegrid", "/@syncfusion/ej2-treemap", "/@syncfusion/ej2-vue-barcode-generator", "/@syncfusion/ej2-vue-base", "/@syncfusion/ej2-vue-calendars", "/@syncfusion/ej2-vue-charts", "/@syncfusion/ej2-vue-diagrams", "/@syncfusion/ej2-vue-documenteditor", "/@syncfusion/ej2-vue-dropdowns", "/@syncfusion/ej2-vue-filemanager", "/@syncfusion/ej2-vue-gantt", "/@syncfusion/ej2-vue-grids", "/@syncfusion/ej2-vue-inputs", "/@syncfusion/ej2-vue-navigations", "/@syncfusion/ej2-vue-pdfviewer", "/@syncfusion/ej2-vue-pivotview", "/@syncfusion/ej2-vue-popups", "/@syncfusion/ej2-vue-querybuilder", "/@syncfusion/ej2-vue-richtexteditor", "/@syncfusion/ej2-vue-schedule", "/@syncfusion/ej2-vue-spreadsheet", "/@syncfusion/ej2-vue-treegrid", "/@syncfusion/ej2-vue-treemap"], "_resolved": "https://nexus.syncfusioninternal.com/repository/ej2-hotfix-new/@syncfusion/ej2-base/-/ej2-base-27.1.53.tgz", "_shasum": "5440a2b6b8b0a752012bddf8105cef096ca36318", "_spec": "@syncfusion/ej2-base@*", "_where": "/jenkins/workspace/elease-automation_release_27.1.1/packages/included", "author": {"name": "Syncfusion Inc."}, "bin": {"syncfusion-license": "bin/syncfusion-license.js"}, "bugs": {"url": "https://github.com/syncfusion/ej2-javascript-ui-controls/issues"}, "bundleDependencies": false, "dependencies": {"@syncfusion/ej2-icons": "~27.1.50"}, "deprecated": false, "description": "A common package of Essential JS 2 base libraries, methods and class definitions", "devDependencies": {}, "es2015": "./dist/es6/ej2-base.es5.js", "homepage": "https://www.syncfusion.com/javascript-ui-controls", "keywords": ["ej2", "syncfusion", "ej2-base", "web-components", "typescript", "base", "common", "core", "library", "ajax", "animation", "internationalization", "drag", "drop", "drag-drop", "template", "template-engine", "rtl", "right-to-left", "persistence", "state-persistence", "theme", "styles"], "license": "SEE LICENSE IN license", "main": "./dist/ej2-base.umd.min.js", "module": "./index.js", "name": "@syncfusion/ej2-base", "repository": {"type": "git", "url": "git+https://github.com/syncfusion/ej2-javascript-ui-controls.git"}, "typings": "index.d.ts", "version": "27.1.55", "sideEffects": true}