{"_from": "@syncfusion/ej2-excel-export@*", "_id": "@syncfusion/ej2-excel-export@27.1.48", "_inBundle": false, "_integrity": "sha512-ERHrQSjkcC3VkUa6ISLtBZA/Kfnb6o2WEUkmuPatTcU/5sd3XpUCNGAOa4e3w9ilXpXcZ8VaDYnmfCryjP3Gtw==", "_location": "/@syncfusion/ej2-excel-export", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@syncfusion/ej2-excel-export@*", "name": "@syncfusion/ej2-excel-export", "escapedName": "@syncfusion%2fej2-excel-export", "scope": "@syncfusion", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/", "/@syncfusion/ej2", "/@syncfusion/ej2-charts", "/@syncfusion/ej2-grids", "/@syncfusion/ej2-pivotview", "/@syncfusion/ej2-schedule"], "_resolved": "https://nexus.syncfusioninternal.com/repository/ej2-hotfix-new/@syncfusion/ej2-excel-export/-/ej2-excel-export-27.1.48.tgz", "_shasum": "1c88d6922bc1ee18cb1a496d7e72b6e3bee58441", "_spec": "@syncfusion/ej2-excel-export@*", "_where": "/jenkins/workspace/elease-automation_release_27.1.1/packages/included", "author": {"name": "Syncfusion Inc."}, "bugs": {"url": "https://github.com/syncfusion/ej2-javascript-ui-controls/issues"}, "bundleDependencies": false, "dependencies": {"@syncfusion/ej2-base": "~27.1.50", "@syncfusion/ej2-compression": "~27.1.50"}, "deprecated": false, "description": "Essential Javascript 2 Excel Export Library", "devDependencies": {}, "es2015": "./dist/es6/ej2-excel-export.es5.js", "homepage": "https://www.syncfusion.com/javascript-ui-controls", "keywords": ["ej2", "syncfusion", "ej2-excel-creator"], "license": "SEE LICENSE IN license", "main": "./dist/ej2-excel-export.umd.min.js", "module": "./index.js", "name": "@syncfusion/ej2-excel-export", "repository": {"type": "git", "url": "git+https://github.com/syncfusion/ej2-javascript-ui-controls.git"}, "typings": "index.d.ts", "version": "27.1.50", "sideEffects": false}