{"_from": "@syncfusion/ej2-popups@*", "_id": "@syncfusion/ej2-popups@27.1.55", "_inBundle": false, "_integrity": "sha512-4n<PERSON>cjHTfiCStstpCCJZ1etScaTxH5vI8BIrWsJEheTVkAZ3Pq/aWFSAg0TuLthEur7eguYOI+Rq3kpDKeKf0w==", "_location": "/@syncfusion/ej2-popups", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@syncfusion/ej2-popups@*", "name": "@syncfusion/ej2-popups", "escapedName": "@syncfusion%2fej2-popups", "scope": "@syncfusion", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/", "/@syncfusion/ej2", "/@syncfusion/ej2-angular-popups", "/@syncfusion/ej2-calendars", "/@syncfusion/ej2-diagrams", "/@syncfusion/ej2-documenteditor", "/@syncfusion/ej2-dropdowns", "/@syncfusion/ej2-filemanager", "/@syncfusion/ej2-gantt", "/@syncfusion/ej2-grids", "/@syncfusion/ej2-image-editor", "/@syncfusion/ej2-inplace-editor", "/@syncfusion/ej2-inputs", "/@syncfusion/ej2-kanban", "/@syncfusion/ej2-lists", "/@syncfusion/ej2-navigations", "/@syncfusion/ej2-notifications", "/@syncfusion/ej2-pdfviewer", "/@syncfusion/ej2-pivotview", "/@syncfusion/ej2-react-popups", "/@syncfusion/ej2-ribbon", "/@syncfusion/ej2-richtexteditor", "/@syncfusion/ej2-schedule", "/@syncfusion/ej2-splitbuttons", "/@syncfusion/ej2-treegrid", "/@syncfusion/ej2-vue-popups"], "_resolved": "https://nexus.syncfusioninternal.com/repository/ej2-hotfix-new/@syncfusion/ej2-popups/-/ej2-popups-27.1.55.tgz", "_shasum": "c8407ec6710b3db2ed8f565f22be9a77737b8698", "_spec": "@syncfusion/ej2-popups@*", "_where": "/jenkins/workspace/elease-automation_release_27.1.1/packages/included", "author": {"name": "Syncfusion Inc."}, "bugs": {"url": "https://github.com/syncfusion/ej2-javascript-ui-controls/issues"}, "bundleDependencies": false, "dependencies": {"@syncfusion/ej2-base": "~27.1.55", "@syncfusion/ej2-buttons": "~27.1.53"}, "deprecated": false, "description": "A package of Essential JS 2 popup components such as Dialog and Tooltip that is used to display information or messages in separate pop-ups.", "devDependencies": {}, "es2015": "./dist/es6/ej2-popups.es5.js", "homepage": "https://www.syncfusion.com/javascript-ui-controls", "keywords": ["ej2", "syncfusion", "web-components", "javascript", "typescript", "dialog", "modal", "popup", "alert", "tooltip", "hint", "spinner", "waiting-popup", "loading-indicator", "loader", "busy-indicator", "waitingfor-loader"], "license": "SEE LICENSE IN license", "main": "./dist/ej2-popups.umd.min.js", "module": "./index.js", "name": "@syncfusion/ej2-popups", "repository": {"type": "git", "url": "git+https://github.com/syncfusion/ej2-javascript-ui-controls.git"}, "typings": "index.d.ts", "version": "27.1.56", "sideEffects": false}