{"_from": "@syncfusion/ej2-inputs@*", "_id": "@syncfusion/ej2-inputs@27.1.48", "_inBundle": false, "_integrity": "sha512-NPwu55QpZMgTe1SVOrCzDsuk80hBTi9AQ4qtBmTdctLcXWKJTQP8mYiE29VNnjIHAxRpnxfpkKI1S03Y1L3/wA==", "_location": "/@syncfusion/ej2-inputs", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@syncfusion/ej2-inputs@*", "name": "@syncfusion/ej2-inputs", "escapedName": "@syncfusion%2fej2-inputs", "scope": "@syncfusion", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/", "/@syncfusion/ej2", "/@syncfusion/ej2-angular-inputs", "/@syncfusion/ej2-calendars", "/@syncfusion/ej2-diagrams", "/@syncfusion/ej2-documenteditor", "/@syncfusion/ej2-dropdowns", "/@syncfusion/ej2-filemanager", "/@syncfusion/ej2-gantt", "/@syncfusion/ej2-grids", "/@syncfusion/ej2-image-editor", "/@syncfusion/ej2-inplace-editor", "/@syncfusion/ej2-interactive-chat", "/@syncfusion/ej2-kanban", "/@syncfusion/ej2-navigations", "/@syncfusion/ej2-pdfviewer", "/@syncfusion/ej2-pivotview", "/@syncfusion/ej2-querybuilder", "/@syncfusion/ej2-react-inputs", "/@syncfusion/ej2-richtexteditor", "/@syncfusion/ej2-schedule", "/@syncfusion/ej2-vue-inputs"], "_resolved": "https://nexus.syncfusioninternal.com/repository/ej2-hotfix-new/@syncfusion/ej2-inputs/-/ej2-inputs-27.1.48.tgz", "_shasum": "86cc234fd0378a4c5689e70000fec1f827114e43", "_spec": "@syncfusion/ej2-inputs@*", "_where": "/jenkins/workspace/elease-automation_release_27.1.1/packages/included", "author": {"name": "Syncfusion Inc."}, "bundleDependencies": false, "dependencies": {"@syncfusion/ej2-base": "~27.1.50", "@syncfusion/ej2-buttons": "~27.1.50", "@syncfusion/ej2-popups": "~27.1.50", "@syncfusion/ej2-splitbuttons": "~27.1.50"}, "deprecated": false, "description": "A package of Essential JS 2 input components such as Textbox, Color-picker, Masked-textbox, Numeric-textbox, Slider, Upload, and Form-validator that is used to get input from the users.", "devDependencies": {}, "es2015": "./dist/es6/ej2-inputs.es5.js", "keywords": ["ej2", "syncfusion", "web-components", "ej2-inputs", "input box", "textbox", "html5 textbox", "floating input", "floating label", "form controls", "input controls", "color", "color picker", "colorpicker", "picker", "palette", "hsv colorpicker", "alpha colorpicker", "color palette", "custom palette", "ej2 colorpicker", "color chooser", "validator", "form", "form validator", "masked textbox", "masked input", "input mask", "date mask", "mask format", "numeric textbox", "percent textbox", "percentage textbox", "currency textbox", "numeric spinner", "numeric up-down", "number input", "slider", "range slider", "minrange", "slider limits", "localization slider", "format slider", "slider with tooltip", "vertical slider", "mobile slider", "upload", "upload-box", "input-file", "floating-label", "chunk-upload"], "license": "SEE LICENSE IN license", "main": "./dist/ej2-inputs.umd.min.js", "module": "./index.js", "name": "@syncfusion/ej2-inputs", "repository": {"type": "git", "url": "https://github.com/syncfusion/ej2-javascript-ui-controls/tree/master/controls/inputs"}, "typings": "index.d.ts", "version": "27.1.50", "sideEffects": false, "homepage": "https://www.syncfusion.com/javascript-ui-controls"}